<template>
    <CheckoutLayout>
        <template #header>
            <CheckoutProgress :step="1" :total="5" />
        </template>
        <PhoneNumberStep @next="goNext" />
    </CheckoutLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useHead } from '@unhead/vue'
import CheckoutLayout from '../components/layouts/CheckoutLayout.vue'
import CheckoutProgress from '../components/checkout/CheckoutProgress.vue'
import PhoneNumberStep from '../components/checkout/steps/PhoneNumberStep.vue'
import { useCheckoutStore } from '../stores/checkout'

const router = useRouter()
useHead({ title: 'Enter Phone', meta: [{ name: 'robots', content: 'noindex' }] })

const store = useCheckoutStore()

function goNext() {
    router.push('/otp')
}

onMounted(() => {
    store.resetFlow()
})
</script>
