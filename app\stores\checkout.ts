import { defineStore } from 'pinia'

export type CurrencyCode = 'GHS' | 'KES' | 'NGN' | 'UGX' | 'ZMW' | 'XOF' | 'XAF' | 'ZAR'
export type MobileMoneyProvider = 'MTN' | 'Airtel' | 'Vodafone' | 'Tigo' | 'Orange' | 'M-Pesa' | 'MoMo'

export interface OrderItem {
  id: string
  name: string
  qty: number
  unitPrice: number // minor units (e.g., cents)
  currency: CurrencyCode
}

export interface CheckoutState {
  step: 1 | 2 | 3 | 4 | 5
  phoneE164: string | null
  otp: string | null
  items: OrderItem[]
  currency: CurrencyCode
  provider: MobileMoneyProvider | null
  amountMinor: number // total in minor units
  termsAccepted: boolean
  loading: boolean
  error: string | null
  txRef: string | null
}

export const useCheckoutStore = defineStore('checkout', {
  state: (): CheckoutState => ({
    step: 1,
    phoneE164: null,
    otp: null,
    items: [],
    currency: 'GHS',
    provider: null,
    amountMinor: 0,
    termsAccepted: false,
    loading: false,
    error: null,
    txRef: null,
  }),
  getters: {
    totalMinor: (state) =>
      state.items.reduce((sum, i) => sum + i.unitPrice * i.qty, 0),
    stepIndex: (state) => state.step,
  },
  actions: {
    setItems(items: OrderItem[]) {
      this.items = items
      this.amountMinor = this.totalMinor
    },
    setPhone(e164: string) {
      this.phoneE164 = e164
    },
    setOtp(otp: string) {
      this.otp = otp
    },
    setProvider(p: MobileMoneyProvider) {
      this.provider = p
    },
    acceptTerms(v: boolean) {
      this.termsAccepted = v
    },
    setError(msg: string | null) {
      this.error = msg
    },
    setLoading(v: boolean) {
      this.loading = v
    },
    next() {
      if (this.step < 5) this.step = (this.step + 1) as CheckoutState['step']
    },
    prev() {
      if (this.step > 1) this.step = (this.step - 1) as CheckoutState['step']
    },
    resetFlow() {
      this.$reset()
    },
    setTxRef(ref: string | null) {
      this.txRef = ref
    },
  },
})
