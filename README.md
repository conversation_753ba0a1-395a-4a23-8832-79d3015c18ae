# Checkout Web (Embeddable SPA)

Nuxt 3 + Vuetify 3 single-page checkout designed to embed into merchant sites via iframe or script tag.

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## Embedding

After `pnpm generate`, host `./.output/public` or `./dist` (depending on Nuxt version) on a static server.

Example iframe:

```html
<iframe
  src="https://your-cdn.example.com/checkout/index.html"
  width="100%"
  height="720"
  style="border:0;"
  allow="payment"
></iframe>
```
