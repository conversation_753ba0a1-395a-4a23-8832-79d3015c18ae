{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@mdi/font": "^7.4.47", "@nuxt/content": "3.6.3", "@nuxt/eslint": "1.9.0", "@nuxt/image": "1.11.0", "@nuxt/scripts": "0.11.13", "@nuxt/test-utils": "3.19.2", "@nuxt/ui": "3.3.2", "@unhead/vue": "^2.0.14", "@vueuse/core": "^11.1.0", "better-sqlite3": "^12.2.0", "eslint": "^9.34.0", "libphonenumber-js": "^1.11.16", "nuxt": "^4.0.3", "pinia": "^2.2.6", "typescript": "^5.9.2", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b", "devDependencies": {"@pinia/nuxt": "^0.5.5", "vite-plugin-vuetify": "^2.1.2", "vuetify": "^3.9.6"}}