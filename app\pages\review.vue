<template>
    <CheckoutLayout>
        <template #header>
            <CheckoutProgress :step="3" :total="5" />
        </template>
        <ReviewStep @back="goBack" @next="goNext" />
    </CheckoutLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useHead } from '@unhead/vue'
import CheckoutLayout from '../components/layouts/CheckoutLayout.vue'
import CheckoutProgress from '../components/checkout/CheckoutProgress.vue'
import ReviewStep from '../components/checkout/steps/ReviewStep.vue'

const router = useRouter()
useHead({ title: 'Review Order' })

function goBack() { router.push('/otp') }
function goNext() { router.push('/pay') }
</script>
