<template>
    <div>
        <slot />
        <v-snackbar v-model="open" color="error" timeout="4000">
            {{ message }}
        </v-snackbar>
    </div>

</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
const open = ref(false)
const message = ref('')
onErrorCaptured((err) => {
    message.value = (err as Error).message || 'Something went wrong'
    open.value = true
    return false
})
</script>
