<template>
    <div class="d-flex align-center justify-space-between" aria-label="Checkout progress">
        <div class="text-subtitle-2">Step {{ step }} / {{ total }}</div>
        <v-progress-linear :model-value="percent" height="6" rounded color="primary" class="flex-1 ml-4" />
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps<{ step: number; total: number }>()
const percent = computed(() => Math.min(100, Math.max(0, (props.step / props.total) * 100)))
</script>

<style scoped>
.flex-1 {
    flex: 1 1 auto;
}
</style>
