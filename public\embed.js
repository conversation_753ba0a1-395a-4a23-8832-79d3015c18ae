(function () {
  function buildSrc(base, params) {
    const url = new URL(base, window.location.origin);
    Object.entries(params || {}).forEach(([k, v]) =>
      url.searchParams.set(k, String(v))
    );
    return url.toString();
  }
  window.initCheckoutWidget = function initCheckoutWidget(options) {
    const {
      target,
      src = "/index.html",
      params,
      width = "100%",
      height = "700px",
      allow = "payment",
    } = options || {};
    const el =
      typeof target === "string" ? document.querySelector(target) : target;
    if (!el) {
      throw new Error("initCheckoutWidget: target not found");
    }
    const iframe = document.createElement("iframe");
    iframe.src = buildSrc(src, params);
    iframe.style.border = "0";
    iframe.width = String(width);
    iframe.height = String(height);
    iframe.allow = allow;
    el.innerHTML = "";
    el.appendChild(iframe);
    return iframe;
  };
})();
