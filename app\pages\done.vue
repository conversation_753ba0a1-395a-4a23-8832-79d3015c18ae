<template>
    <CheckoutLayout>
        <template #header>
            <CheckoutProgress :step="5" :total="5" />
        </template>
        <ConfirmationStep />
    </CheckoutLayout>
</template>

<script setup lang="ts">
import { useHead } from '@unhead/vue'
import CheckoutLayout from '../components/layouts/CheckoutLayout.vue'
import CheckoutProgress from '../components/checkout/CheckoutProgress.vue'
import ConfirmationStep from '../components/checkout/steps/ConfirmationStep.vue'

useHead({ title: 'Payment Status', meta: [{ name: 'robots', content: 'noindex' }] })
</script>
