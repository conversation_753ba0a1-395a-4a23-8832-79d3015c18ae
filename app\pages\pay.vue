<template>
    <CheckoutLayout>
        <template #header>
            <CheckoutProgress :step="4" :total="5" />
        </template>
        <PaymentStep @back="goBack" @next="goNext" />
    </CheckoutLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useHead } from '@unhead/vue'
import CheckoutLayout from '../components/layouts/CheckoutLayout.vue'
import CheckoutProgress from '../components/checkout/CheckoutProgress.vue'
import PaymentStep from '../components/checkout/steps/PaymentStep.vue'

const router = useRouter()
useHead({ title: 'Payment', meta: [{ name: 'robots', content: 'noindex' }] })

function goBack() { router.push('/review') }
function goNext() { router.push('/done') }
</script>
