<template>
    <CheckoutLayout>
        <template #header>
            <CheckoutProgress :step="2" :total="5" />
        </template>
        <OtpStep @next="goNext" @back="goBack" />
    </CheckoutLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useHead } from '@unhead/vue'
import CheckoutLayout from '../components/layouts/CheckoutLayout.vue'
import CheckoutProgress from '../components/checkout/CheckoutProgress.vue'
import OtpStep from '../components/checkout/steps/OtpStep.vue'

const router = useRouter()
useHead({ title: 'Verify OTP', meta: [{ name: 'robots', content: 'noindex' }] })

function goNext() { router.push('/review') }
function goBack() { router.push('/') }
</script>
