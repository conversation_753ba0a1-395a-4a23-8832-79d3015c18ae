<template>
    <div class="text-center py-6">
        <v-icon size="56" color="success" v-if="success">mdi-check-circle</v-icon>
        <v-icon size="56" color="error" v-else>mdi-alert-circle</v-icon>
        <div class="text-h6 mt-3">{{ success ? 'Payment successful' : 'Payment failed' }}</div>
        <div class="text-body-2 text-medium-emphasis mt-1">Reference: {{ ref || '—' }}</div>
        <v-btn color="primary" class="mt-6" @click="goBack">Return to merchant</v-btn>
        <div class="mt-4 d-flex gap-2 justify-center">
            <v-btn variant="tonal" @click="download">Download receipt</v-btn>
            <v-btn variant="tonal" @click="email">Email receipt</v-btn>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCheckoutStore } from '@/stores/checkout'

const store = useCheckoutStore()
const ref = computed(() => store.txRef)
const success = computed(() => !!store.txRef)

function goBack() {
    // In an embedded context, this might postMessage to parent; for now navigate home
    navigateTo('/')
}

function download() {
    // TODO: generate PDF/HTML receipt
}

function email() {
    // TODO: trigger email flow
}
</script>
