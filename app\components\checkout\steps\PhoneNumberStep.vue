<template>
    <v-form @submit.prevent="onSubmit" v-model="valid" validate-on="input" ref="formRef">
        <v-row dense>
            <v-col cols="12">
                <v-text-field v-model="input" :label="'Phone number'" :hint="hint" persistent-hint :rules="rules"
                    type="tel" autocomplete="tel" prepend-inner-icon="mdi-cellphone" clearable required />
            </v-col>
            <v-col cols="12">
                <v-btn :loading="loading" color="primary" block type="submit">Continue</v-btn>
            </v-col>
        </v-row>
    </v-form>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { parsePhoneNumberFromString } from 'libphonenumber-js'
import { useCheckoutStore } from '@/stores/checkout'

const emit = defineEmits<{ (e: 'next'): void }>()
const store = useCheckoutStore()

const input = ref('')
const valid = ref(false)
const loading = ref(false)
const formRef = ref()

const hint = computed(() => 'Include country code, e.g. +233 20 123 4567')

const rules = [
    (v: string) => !!v || 'Phone is required',
    (v: string) => {
        try {
            const p = parsePhoneNumberFromString(v)
            return (p && p.isValid()) || 'Enter a valid phone number with country code'
        } catch {
            return 'Enter a valid phone number'
        }
    },
]

async function onSubmit() {
    const ok = await formRef.value?.validate()
    if (!ok?.valid) return
    try {
        loading.value = true
        const p = parsePhoneNumberFromString(input.value)
        if (!p || !p.isValid()) throw new Error('Invalid phone')
        store.setPhone(p.number)
        // simulate sending OTP
        await new Promise((r) => setTimeout(r, 600))
        emit('next')
    } catch (e) {
        // surface friendly error
    } finally {
        loading.value = false
    }
}
</script>
