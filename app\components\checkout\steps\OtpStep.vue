<template>
    <div>
        <v-btn variant="text" prepend-icon="mdi-arrow-left" @click="$emit('back')">Back</v-btn>
        <v-form v-model="valid" @submit.prevent="onSubmit" ref="formRef">
            <v-otp-input v-model="otp" length="6" :rules="rules" />
            <div class="d-flex align-center justify-space-between mt-4">
                <v-btn :disabled="countdown > 0" variant="tonal" @click="resend">Resend {{ countdown > 0 ? `in
                    ${countdown}s`:'' }}</v-btn>
                <v-btn color="primary" type="submit" :loading="loading">Verify</v-btn>
            </div>
            <p v-if="error" class="text-error mt-2" role="alert">{{ error }}</p>
        </v-form>
    </div>

</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useCheckoutStore } from '@/stores/checkout'

const emit = defineEmits<{ (e: 'next'): void; (e: 'back'): void }>()

const store = useCheckoutStore()
const otp = ref('')
const valid = ref(false)
const loading = ref(false)
const error = ref<string | null>(null)
const formRef = ref()
const countdown = ref(30)
let timer: any

const rules = [
    (v: string) => !!v || 'Code required',
    (v: string) => /\d{4,6}/.test(v) || 'Enter 4-6 digit code',
]

onMounted(() => startTimer())
onUnmounted(() => clearInterval(timer))

function startTimer() {
    countdown.value = 30
    clearInterval(timer)
    timer = setInterval(() => {
        countdown.value -= 1
        if (countdown.value <= 0) clearInterval(timer)
    }, 1000)
}

async function resend() {
    startTimer()
}

async function onSubmit() {
    const ok = await formRef.value?.validate()
    if (!ok?.valid) return
    loading.value = true
    error.value = null
    try {
        // mock verify
        await new Promise((r) => setTimeout(r, 500))
        store.setOtp(otp.value)
        emit('next')
    } catch (e) {
        error.value = 'Invalid or expired code'
    } finally {
        loading.value = false
    }
}
</script>
