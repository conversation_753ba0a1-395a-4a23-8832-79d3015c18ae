<template>
    <div>
        <v-btn variant="text" prepend-icon="mdi-arrow-left" @click="$emit('back')">Back</v-btn>
        <v-list density="compact" class="mt-2" lines="two">
            <v-list-item v-for="item in items" :key="item.id">
                <template #prepend>
                    <v-avatar color="primary" size="28"><span class="text-caption">{{ item.qty }}</span></v-avatar>
                </template>
                <v-list-item-title>{{ item.name }}</v-list-item-title>
                <v-list-item-subtitle>{{ formatCurrency(item.unitPrice * item.qty, item.currency)
                    }}</v-list-item-subtitle>
            </v-list-item>
        </v-list>
        <v-divider class="my-4" />
        <div class="d-flex align-center justify-space-between">
            <div class="text-subtitle-1 font-weight-medium">Total</div>
            <div class="text-h6">{{ formatCurrency(total, currency) }}</div>
        </div>
        <v-checkbox v-model="accepted" label="I accept the terms and conditions" class="mt-3" />
        <v-btn color="primary" class="mt-4" :disabled="!accepted" @click="$emit('next')" block>Continue to
            payment</v-btn>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useCheckoutStore } from '@/stores/checkout'
import { formatCurrency } from '@/utils/money'

const store = useCheckoutStore()
const items = computed(() => store.items)
const currency = computed(() => store.currency)
const total = computed(() => store.totalMinor)
const accepted = ref(store.termsAccepted)

watch(accepted, (v) => store.acceptTerms(!!v))
</script>
