<template>
    <v-container class="d-flex flex-column align-center justify-start" fluid>
        <v-sheet elevation="0" max-width="640" width="100%" class="mx-auto">
            <div class="py-3">
                <slot name="header" />
            </div>
            <v-card rounded="xl" elevation="1">
                <v-card-text class="pa-4 pa-sm-6">
                    <slot />
                </v-card-text>
            </v-card>
            <div class="py-4 text-caption text-medium-emphasis text-center">
                <slot name="footer">Secure checkout</slot>
            </div>
        </v-sheet>
    </v-container>

</template>

<script setup lang="ts">
// Layout for checkout steps
</script>

<style scoped>
/* Mobile-first spacing handled by Vuetify utility classes */
</style>
