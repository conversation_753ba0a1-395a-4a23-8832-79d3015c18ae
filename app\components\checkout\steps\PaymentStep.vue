<template>
    <div>
        <v-btn variant="text" prepend-icon="mdi-arrow-left" @click="$emit('back')">Back</v-btn>

        <v-alert type="info" variant="tonal" class="mt-2">Confirm your payment details</v-alert>

        <v-form v-model="valid" @submit.prevent="onSubmit" ref="formRef" class="mt-4">
            <v-select v-model="provider" :items="providers" label="Mobile Money Provider"
                :rules="[(v: any) => !!v || 'Select a provider']" prepend-inner-icon="mdi-sim" />

            <v-text-field v-model="amount" label="Amount" type="number" :rules="[(v: any) => v > 0 || 'Amount is required']"
                prepend-inner-icon="mdi-currency-usd" readonly />

            <v-btn color="primary" type="submit" :loading="loading" block>Pay now</v-btn>
        </v-form>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useCheckoutStore } from '@/stores/checkout'
const emit = defineEmits<{ (e: 'next'): void; (e: 'back'): void }>()

const store = useCheckoutStore()
const valid = ref(false)
const formRef = ref()
const loading = ref(false)

const providers = ['MTN', 'Airtel', 'Vodafone', 'M-Pesa', 'MoMo']
const provider = ref(store.provider)
const amount = computed(() => (store.amountMinor / 100).toFixed(2))

watch(provider, (v) => store.setProvider(v as any))

async function onSubmit() {
    const ok = await formRef.value?.validate()
    if (!ok?.valid) return
    loading.value = true
    try {
        // mock processing
        await new Promise((r) => setTimeout(r, 1200))
        store.setTxRef(`TX-${Date.now()}`)
        emit('next')
    } finally {
        loading.value = false
    }
}
</script>
