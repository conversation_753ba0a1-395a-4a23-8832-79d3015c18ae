// import this after install `@mdi/font` package
import '@mdi/font/css/materialdesignicons.css'

import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import { VBtn, VCard, VTextField, VSelect } from 'vuetify/components'
// import { VOtpInput } from 'vuetify/labs'

// Manually import for now since auto-import isn't working
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - defineNuxtPlugin should be auto-imported
export default defineNuxtPlugin((app) => {
  const vuetify = createVuetify({
    theme: {
      defaultTheme: 'light',
      themes: {
        light: {
          colors: {
            primary: "#ff0055",
            secondary: "#000000",
            accent: "#001122",
            surface: "#FFFFFF",
          },
        },
      },
    },
    defaults: {
      global: {
        font: {
          family: "Outfit",
        },
      },
      VTextField: {
        variant: "outlined",
        density: "comfortable",
      },
      VBtn: {
        fontWeight: "500",
      },
      VCard: {
        elevation: 2,
      },
      VSheet: {
        elevation: 0,
      },
    },
    components: { 
      VBtn, 
      VCard, 
      VTextField, 
      VSelect,
      // VOtpInput 
    },
  })
  app.vueApp.use(vuetify)
})
